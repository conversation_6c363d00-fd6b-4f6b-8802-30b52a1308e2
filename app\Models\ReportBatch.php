<?php

namespace App\Models;

use App\Models\Reports\A01;
use App\Models\Reports\D01;
use App\Models\Reports\D02;
use App\Models\Reports\F01;
use App\Models\Reports\F05;
use App\Models\Reports\P01;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ReportBatch extends Model
{
    protected $table = 'report_batches';

    protected $fillable = [
        'institution_code',
        'branch_code',
        'report_code',
        'report_month',
        'report_year',
        'status',
        'record_count',
        'validation_results',
        'error_message',
        'processed_at',
    ];

    protected $casts = [
        'report_month' => 'integer',
        'report_year' => 'integer',
        'record_count' => 'integer',
        'validation_results' => 'array',
        'processed_at' => 'datetime',
    ];

    protected $appends = [
        'period_name',
        'status_label',
        'completion_percentage',
    ];

    // Status constants
    public const STATUS_PENDING = 'pending';
    public const STATUS_PROCESSING = 'processing';
    public const STATUS_COMPLETED = 'completed';
    public const STATUS_FAILED = 'failed';

    /**
     * Get the period name (e.g., "January 2024")
     */
    public function getPeriodNameAttribute(): string
    {
        return Carbon::createFromDate($this->report_year, $this->report_month, 1)->format('F Y');
    }

    /**
     * Get human-readable status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_PROCESSING => 'Processing',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_FAILED => 'Failed',
            default => 'Unknown',
        };
    }

    /**
     * Get completion percentage based on record count
     */
    public function getCompletionPercentageAttribute(): int
    {
        if ($this->status === self::STATUS_COMPLETED) {
            return 100;
        }

        if ($this->status === self::STATUS_FAILED) {
            return 0;
        }

        // For processing status, calculate based on record count vs expected
        return min(($this->record_count / max($this->getExpectedRecordCount(), 1)) * 100, 99);
    }

    /**
     * Get expected record count for this report type
     */
    protected function getExpectedRecordCount(): int
    {
        // This would be configured based on historical data or business rules
        // For now, return a default value
        return 1000;
    }

    /**
     * Scope for filtering by period
     */
    public function scopeForPeriod(Builder $query, int $year, int $month): Builder
    {
        return $query->where('report_year', $year)->where('report_month', $month);
    }

    /**
     * Scope for filtering by status
     */
    public function scopeWithStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for completed reports
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Check if report batch is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if report batch is processing
     */
    public function isProcessing(): bool
    {
        return $this->status === self::STATUS_PROCESSING;
    }

    /**
     * Check if report batch has failed
     */
    public function hasFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Mark as processing
     */
    public function markAsProcessing(): void
    {
        $this->update([
            'status' => self::STATUS_PROCESSING,
            'error_message' => null,
        ]);
    }

    /**
     * Mark as completed
     */
    public function markAsCompleted(int $recordCount = null): void
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'record_count' => $recordCount ?? $this->record_count,
            'processed_at' => now(),
            'error_message' => null,
        ]);
    }

    /**
     * Mark as failed
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'error_message' => $errorMessage,
        ]);
    }

    /**
     * Get related report data based on report code
     */
    public function getReportData()
    {
        return match ($this->report_code) {
            'A01' => $this->a01Reports(),
            'D01' => $this->d01Reports(),
            'D02' => $this->d02Reports(),
            'F01' => $this->f01Reports(),
            'F05' => $this->f05Reports(),
            'P01' => $this->p01Reports(),
            default => collect([]),
        };
    }

    /**
     * Relationship to A01 reports
     */
    public function a01Reports(): HasMany
    {
        return $this->hasMany(A01::class, 'report_batch_id');
    }

    /**
     * Relationship to D01 reports
     */
    public function d01Reports(): HasMany
    {
        return $this->hasMany(D01::class, 'report_batch_id');
    }

    /**
     * Relationship to D02 reports
     */
    public function d02Reports(): HasMany
    {
        return $this->hasMany(D02::class, 'report_batch_id');
    }

    /**
     * Relationship to F01 reports
     */
    public function f01Reports(): HasMany
    {
        return $this->hasMany(F01::class, 'report_batch_id');
    }

    /**
     * Relationship to F05 reports
     */
    public function f05Reports(): HasMany
    {
        return $this->hasMany(F05::class, 'report_batch_id');
    }

    /**
     * Relationship to P01 reports
     */
    public function p01Reports(): HasMany
    {
        return $this->hasMany(P01::class, 'report_batch_id');
    }

    /**
     * Get previous period for comparison
     */
    public function getPreviousPeriod(): array
    {
        $previousMonth = $this->report_month - 1;
        $previousYear = $this->report_year;

        if ($previousMonth < 1) {
            $previousMonth = 12;
            $previousYear--;
        }

        return [
            'year' => $previousYear,
            'month' => $previousMonth,
        ];
    }

    /**
     * Get comparison period (two months prior)
     */
    public function getComparisonPeriod(): array
    {
        $comparisonMonth = $this->report_month - 2;
        $comparisonYear = $this->report_year;

        if ($comparisonMonth < 1) {
            $comparisonMonth += 12;
            $comparisonYear--;
        }

        return [
            'year' => $comparisonYear,
            'month' => $comparisonMonth,
        ];
    }

    /**
     * Check if previous period data exists
     */
    public function hasPreviousPeriodData(): bool
    {
        $previous = $this->getPreviousPeriod();

        return self::where('institution_code', $this->institution_code)
            ->where('branch_code', $this->branch_code)
            ->where('report_code', $this->report_code)
            ->where('report_year', $previous['year'])
            ->where('report_month', $previous['month'])
            ->where('status', self::STATUS_COMPLETED)
            ->exists();
    }

    /**
     * Check if comparison period data exists
     */
    public function hasComparisonPeriodData(): bool
    {
        $comparison = $this->getComparisonPeriod();

        return self::where('institution_code', $this->institution_code)
            ->where('branch_code', $this->branch_code)
            ->where('report_code', $this->report_code)
            ->where('report_year', $comparison['year'])
            ->where('report_month', $comparison['month'])
            ->where('status', self::STATUS_COMPLETED)
            ->exists();
    }
}
