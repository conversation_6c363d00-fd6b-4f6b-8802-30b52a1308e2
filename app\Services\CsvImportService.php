<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Storage;

class CsvImportService
{
    /**
     * Possible CSV separators to detect
     */
    private const SEPARATORS = [';', ',', "\t", '|'];

    /**
     * Parse filename to extract report components
     */
    public function parseFilename(string $filename): array
    {
        // Expected format: {report_code}_{month}{year}.csv
        // Example: A01_042025.csv
        $pattern = '/^([A-Z]\d{2})_(\d{2})(\d{4})\.csv$/i';
        
        if (!preg_match($pattern, $filename, $matches)) {
            throw new Exception("Invalid filename format. Expected format: {report_code}_{month}{year}.csv (e.g., A01_042025.csv)");
        }

        return [
            'report_code' => strtoupper($matches[1]),
            'month' => (int) $matches[2],
            'year' => (int) $matches[3],
        ];
    }

    /**
     * Detect CSV separator by analyzing the first few lines
     */
    public function detectSeparator(string $filePath): string
    {
        if (!Storage::exists($filePath)) {
            throw new Exception("File not found: {$filePath}");
        }

        $content = Storage::get($filePath);
        $lines = explode("\n", $content);
        
        // Take first 3 lines for analysis (header + 2 data lines)
        $sampleLines = array_slice($lines, 0, 3);
        $sampleLines = array_filter($sampleLines, fn($line) => !empty(trim($line)));

        if (empty($sampleLines)) {
            throw new Exception("CSV file is empty or contains no valid data");
        }

        $separatorCounts = [];

        foreach (self::SEPARATORS as $separator) {
            $counts = [];
            foreach ($sampleLines as $line) {
                $counts[] = substr_count($line, $separator);
            }
            
            // Check if separator appears consistently across lines
            $uniqueCounts = array_unique($counts);
            if (count($uniqueCounts) === 1 && $counts[0] > 0) {
                $separatorCounts[$separator] = $counts[0];
            }
        }

        if (empty($separatorCounts)) {
            throw new Exception("Could not detect CSV separator. Please ensure the file uses one of: " . implode(', ', self::SEPARATORS));
        }

        // Return separator with highest count (most columns)
        return array_search(max($separatorCounts), $separatorCounts);
    }

    /**
     * Parse CSV file and return data as array
     */
    public function parseCsv(string $filePath): array
    {
        if (!Storage::exists($filePath)) {
            throw new Exception("File not found: {$filePath}");
        }

        $separator = $this->detectSeparator($filePath);
        $content = Storage::get($filePath);
        
        // Convert to temporary file for fgetcsv
        $tempFile = tmpfile();
        fwrite($tempFile, $content);
        rewind($tempFile);

        $data = [];
        $headers = null;
        $rowNumber = 0;

        while (($row = fgetcsv($tempFile, 0, $separator)) !== false) {
            $rowNumber++;
            
            // Skip empty rows
            if (empty(array_filter($row, fn($cell) => !empty(trim($cell))))) {
                continue;
            }

            if ($headers === null) {
                // First non-empty row is headers
                $headers = array_map('trim', $row);

                // Remove BOM from first header if present
                if (!empty($headers[0])) {
                    $headers[0] = $this->removeBom($headers[0]);
                }

                continue;
            }

            // Combine headers with row data
            $rowData = [];
            foreach ($headers as $index => $header) {
                $rowData[$header] = isset($row[$index]) ? trim($row[$index]) : null;
            }
            
            $data[] = $rowData;
        }

        fclose($tempFile);

        if (empty($data)) {
            throw new Exception("CSV file contains no data rows");
        }

        return [
            'headers' => $headers,
            'data' => $data,
            'separator' => $separator,
            'total_rows' => count($data),
        ];
    }

    /**
     * Validate CSV structure against expected report schema
     */
    public function validateCsvStructure(array $headers, string $reportCode): bool
    {
        $expectedHeaders = $this->getExpectedHeaders($reportCode);
        
        if (empty($expectedHeaders)) {
            // If we don't have specific headers for this report type, just check if we have any headers
            return !empty($headers);
        }

        // Check if all expected headers are present (case-insensitive)
        $normalizedHeaders = array_map('strtoupper', $headers);
        $normalizedExpected = array_map('strtoupper', $expectedHeaders);

        $missingHeaders = array_diff($normalizedExpected, $normalizedHeaders);
        
        if (!empty($missingHeaders)) {
            throw new Exception("Missing required headers: " . implode(', ', $missingHeaders));
        }

        return true;
    }

    /**
     * Get expected headers for each report type
     */
    private function getExpectedHeaders(string $reportCode): array
    {
        // Define expected headers for each report type based on database schema
        $headerMappings = [
            'A01' => [
                'FLAG DETAIL',
                'KODE REGISTER / NOMOR AGUNAN',
                'NOMOR REKENING FASILITAS',
                'NOMOR CIF DEBITUR',
                // Add more headers as needed
            ],
            'D01' => [
                'FLAG DETAIL',
                'NOMOR CIF DEBITUR',
                'NOMOR IDENTITAS DEBITUR',
                // Add more headers as needed
            ],
            'D02' => [
                'FLAG DETAIL',
                'NOMOR CIF DEBITUR',
                'NOMOR IDENTITAS BADAN USAHA',
                // Add more headers as needed
            ],
            'F01' => [
                'FLAG DETAIL',
                'NOMOR REKENING FASILITAS',
                'NOMOR CIF DEBITUR',
                'KODE SIFAT KREDIT /PEMBIAYAAN',
                'KODE JENIS KREDIT / PEMBIAYAAN',
                'KODE SKIM/AKAD PEMBIAYAAN',
                'NOMOR AKAD AWAL',
                'TANGGAL AKAD AWAL',
                'NOMOR AKAD AKHIR',
                'TANGGAL AKAD AKHIR',
                'BARU / PERPANJANGAN',
                'TANGGAL AWAL KREDIT',
                'TANGGAL MULAI',
                'TANGGAL JATUH TEMPO',
                'KODE KATEGORI DEBITUR',
                'KODE JENIS PENGGUNAAN',
                'KODE ORIENTASI PENGGUNAAN',
                'KODE SEKTOR EKONOMI',
                'KODE KAB/KOTA (DATI 2) LOKASI PROYEK',
                'NILAI PROYEK',
                'KODE VALUTA',
                'PERSENTASE SUKU BUNGA/IMBALAN',
                'JENIS SUKU BUNGA/IMBALAN',
                'KREDIT PROGRAM PEMERINTAH',
                'TAKEOVER DARI',
                'SUMBER DANA',
                'PLAFON AWAL',
                'PLAFON',
                'REALISASI / PENCAIRAN BULAN BERJALAN',
                'DENDA',
                'BAKI DEBET',
                'NILAI DALAM MATA UANG ASAL',
                'KODE KOLEKTIBILITAS',
                'TANGGAL MACET',
                'KODE SEBAB MACET',
                'TUNGGAKAN POKOK',
                'TUNGGAKAN BUNGA',
                'JUMLAH HARI TUNGGAKAN',
                'FREKUENSI TUNGGAKAN',
                'FREKUENSI RESTRUKTURISASI',
                'TANGGAL RESTRUKTURISASI AWAL',
                'TANGGAL RESTRUKTURISASI AKHIR',
                'KODE CARA RESTRUKTURISASI',
                'KODE KONDISI',
                'TANGGAL KONDISI',
                'KETERANGAN',
                'KODE KANTOR CABANG',
                'OPERASI DATA',
            ],
            'F05' => [
                'FLAG DETAIL',
                'NOMOR REKENING FASILITAS',
                'NOMOR CIF DEBITUR',
                // Add more headers as needed
            ],
            'P01' => [
                'FLAG DETAIL',
                'NOMOR IDENTITAS PENJAMIN',
                'NOMOR REKENING FASILITAS',
                'NOMOR CIF DEBITUR',
                // Add more headers as needed
            ],
        ];

        return $headerMappings[$reportCode] ?? [];
    }

    /**
     * Remove BOM (Byte Order Mark) from string
     */
    private function removeBom(string $text): string
    {
        // Remove UTF-8 BOM
        if (substr($text, 0, 3) === "\xEF\xBB\xBF") {
            return substr($text, 3);
        }

        // Remove UTF-16 BE BOM
        if (substr($text, 0, 2) === "\xFE\xFF") {
            return substr($text, 2);
        }

        // Remove UTF-16 LE BOM
        if (substr($text, 0, 2) === "\xFF\xFE") {
            return substr($text, 2);
        }

        return $text;
    }
}
