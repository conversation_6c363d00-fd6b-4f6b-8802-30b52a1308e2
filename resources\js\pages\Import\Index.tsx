import { useState } from 'react';
import { Head, router, useForm, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { Eye, Upload, Loader2 } from 'lucide-react';
import { DataTable } from '@/components/ui/data-table';

interface FileData {
    filename: string;
    report_code: string;
    period_name: string;
    size: number;
}

interface ImportData {
    id: number;
    filename: string;
    report_code: string;
    period_name: string;
    records_imported: number;
    status: string;
    status_label: string;
    created_at: string;
}

interface RecentImports {
    data: ImportData[];
}

interface PreviewData {
    filename?: string;
    headers: string[];
    preview_rows: Record<string, any>[];
    total_rows: number;
    separator: string;
}

interface Props {
    availableFiles: FileData[];
    recentImports: RecentImports;
}

export default function ImportIndex({ availableFiles, recentImports }: Props) {
    const [isLoading, setIsLoading] = useState(false);
    const [loadingMessage, setLoadingMessage] = useState('');
    const [showPreview, setShowPreview] = useState(false);
    const [previewData, setPreviewData] = useState<PreviewData>({
        headers: [],
        preview_rows: [],
        total_rows: 0,
        separator: '',
    });
    const [showImportConfirm, setShowImportConfirm] = useState(false);
    const [selectedFile, setSelectedFile] = useState<string>('');

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Import Reports', href: '/import' },
    ];

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const formatDate = (dateString: string): string => {
        return new Date(dateString).toLocaleString();
    };

    const getStatusBadgeVariant = (status: string) => {
        return status === 'success' ? 'default' : 'destructive';
    };

    const getCsrfToken = (): string => {
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!token) {
            throw new Error('CSRF token not found');
        }
        return token;
    };

    const previewFile = async (filename: string) => {
        setIsLoading(true);
        setLoadingMessage('Loading preview...');

        try {
            const response = await fetch(route('import.preview'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': getCsrfToken(),
                },
                body: JSON.stringify({ filename }),
            });

            const result = await response.json();


            if (result.success) {
                setPreviewData({ ...result.data, filename });
                setShowPreview(true);
            } else {
                toast.error('Failed to preview file: ' + result.message);
            }
        } catch (error) {
            toast.error('Error previewing file: ' + (error as Error).message);
        } finally {
            setIsLoading(false);
        }
    };

    const closePreview = () => {
        setShowPreview(false);
        setPreviewData({
            headers: [],
            preview_rows: [],
            total_rows: 0,
            separator: '',
        });
    };

    const confirmImport = (filename: string) => {
        setSelectedFile(filename);
        setShowImportConfirm(true);
    };

    const importFile = async () => {
        setShowImportConfirm(false);
        setIsLoading(true);
        setLoadingMessage('Importing file...');

        try {
            const response = await fetch('/import/file', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': getCsrfToken(),
                },
                body: JSON.stringify({ filename: selectedFile }),
            });

            const result = await response.json();

            if (result.success) {
                toast.success(
                    `Successfully imported ${result.data.records_imported} records from ${selectedFile}`
                );
                // Refresh the page to show updated recent imports
                router.reload();
            } else {
                toast.error('Import failed: ' + result.message);
            }
        } catch (error) {
            console.log({ error });

            toast.error('Error importing file: ' + (error as Error).message);
        } finally {
            setIsLoading(false);
            setSelectedFile('');
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Import Reports" />

            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                {/* Available Files Section */}
                <div className="bg-card rounded-lg border p-6">
                    <h3 className="text-lg font-medium mb-4">Available CSV Files</h3>

                    {availableFiles.length === 0 ? (
                        <p className="text-muted-foreground">
                            No CSV files found in the seed directory.
                        </p>
                    ) : (
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Filename</TableHead>
                                        <TableHead>Report Code</TableHead>
                                        <TableHead>Period</TableHead>
                                        <TableHead>Size</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {availableFiles.map((file) => (
                                        <TableRow key={file.filename}>
                                            <TableCell className="font-medium">
                                                {file.filename}
                                            </TableCell>
                                            <TableCell>{file.report_code}</TableCell>
                                            <TableCell>{file.period_name}</TableCell>
                                            <TableCell>{formatFileSize(file.size)}</TableCell>
                                            <TableCell>
                                                <div className="flex gap-2">
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => previewFile(file.filename)}
                                                        disabled={isLoading}
                                                    >
                                                        <Eye className="h-4 w-4 mr-1" />
                                                        Preview
                                                    </Button>
                                                    <Button
                                                        size="sm"
                                                        onClick={() => confirmImport(file.filename)}
                                                        disabled={isLoading}
                                                    >
                                                        <Upload className="h-4 w-4 mr-1" />
                                                        Import
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    )}
                </div>

                {/* Recent Imports Section */}
                <div className="bg-card rounded-lg border p-6">
                    <h3 className="text-lg font-medium mb-4">Recent Imports</h3>

                    {recentImports.data.length === 0 ? (
                        <p className="text-muted-foreground">
                            No recent imports found.
                        </p>
                    ) : (
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Filename</TableHead>
                                        <TableHead>Report Code</TableHead>
                                        <TableHead>Period</TableHead>
                                        <TableHead>Records</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Date</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {recentImports.data.map((import_) => (
                                        <TableRow key={import_.id}>
                                            <TableCell className="font-medium">
                                                {import_.filename}
                                            </TableCell>
                                            <TableCell>{import_.report_code}</TableCell>
                                            <TableCell>{import_.period_name}</TableCell>
                                            <TableCell>
                                                {import_.records_imported.toLocaleString()}
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant={getStatusBadgeVariant(import_.status)}>
                                                    {import_.status_label}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>{formatDate(import_.created_at)}</TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    )}
                </div>
            </div>

            {/* Loading Overlay */}
            {isLoading && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                    <div className="bg-card p-6 rounded-lg shadow-lg">
                        <div className="flex items-center gap-3">
                            <Loader2 className="h-6 w-6 animate-spin" />
                            <span className="text-lg">{loadingMessage}</span>
                        </div>
                    </div>
                </div>
            )}

            {/* Preview Modal */}
            <Dialog open={showPreview} onOpenChange={setShowPreview}>
                <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto w-full">
                    <DialogHeader>
                        <DialogTitle>CSV Preview: {previewData.filename}</DialogTitle>
                    </DialogHeader>

                    <div className="space-y-4 w-full">
                        <div className="text-sm text-muted-foreground">
                            Total Rows: {previewData.total_rows} |
                            Separator: {previewData.separator === ';' ? 'Semicolon' :
                                previewData.separator === ',' ? 'Comma' : 'Tab'}
                        </div>

                        <DataTable
                            columns={previewData.headers.map(header => ({
                                accessorKey: header,
                                header: header,
                                cell: ({ row }) => (
                                    <div className="text-xs">
                                        {row.getValue(header) || '-'}
                                    </div>
                                ),
                            }))}
                            data={previewData.preview_rows as unknown as any[]}
                            // className="rounded-md border overflow-x-auto"
                        />
                        <div className="flex justify-end">
                            <Button variant="outline" onClick={closePreview}>
                                Close
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Import Confirmation Dialog */}
            <AlertDialog open={showImportConfirm} onOpenChange={setShowImportConfirm}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirm Import</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to import {selectedFile}? This will replace any existing data for this report period.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={importFile}>
                            Import
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
